"""
RenoRetail API - FastAPI application with JWT authentication middleware.

This module provides a REST API for the RenoRetail application with automatic
JWT authentication using AWS Cognito through custom middleware.
"""
import json
import os
from typing import Optional
from uuid import uuid4
from datetime import datetime

import boto3
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, status, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from mangum import Mangum
from botocore.exceptions import ClientError

from middleware import AuthenticationMiddleware
from models import *
import traceback
from fastapi.security import OAuth2PasswordBearer
from dotenv import load_dotenv

load_dotenv(override=True)

# Add this import near the top with other imports
from routers.users import router as users_router
from routers.workspaces import router as workspaces_router

# Initialize FastAPI app
app = FastAPI(
    title="RenoRetail API",
    version="1.0.0",
    docs_url="/docs",
    openapi_url="/openapi.json",
    redoc_url="/redoc",
    root_path='/dev'
)

# Add authentication middleware
# Cognito configuration is now handled in middleware.py via environment variables
app.add_middleware(
    AuthenticationMiddleware,
    excluded_paths=["/docs", "/openapi.json", "/redoc", "/health", "/login"]
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type"]
)

# AWS Setup
S3_BUCKET = os.getenv("S3_BUCKET", "my-planogram-bucket")
USERS_TABLE = os.getenv("USERS_TABLE", "users")
COMPLIANCERESULTS_TABLE = os.getenv("COMPLIANCERESULTS_TABLE", "compliance-results")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")

s3_client = boto3.client("s3")
dynamodb = boto3.resource(
    "dynamodb", 
    region_name=AWS_REGION,
    aws_access_key_id='********************',
    aws_secret_access_key='QmNkZyl3J/sV6lQodNLrqDxplKHtfRYLa5xyNh8p'
)

ddb_users_table = dynamodb.Table(USERS_TABLE)
ddb_compliance_results_table = dynamodb.Table(COMPLIANCERESULTS_TABLE)


@app.get("/health")
def health():
    """Health check endpoint - no authentication required."""
    return {"message": "RenoRetail API is running", "status": "healthy"}


# TODO: remove this
USER_POOL_ID = os.getenv("USER_POOL_ID", "us-east-1_XXXXXXX")
APP_CLIENT_ID = os.getenv("APP_CLIENT_ID", "your_client_id")
cognito_client = boto3.client("cognito-idp", region_name=AWS_REGION)

@app.post("/login")
def login(request: LoginRequest):
    try:
        response = cognito_client.initiate_auth(
            ClientId=APP_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": request.username,
                "PASSWORD": request.password
            }
        )
        return {
            "id_token": response["AuthenticationResult"]["IdToken"],
            "access_token": response["AuthenticationResult"]["AccessToken"],
            "refresh_token": response["AuthenticationResult"]["RefreshToken"],
            "expires_in": response["AuthenticationResult"]["ExpiresIn"]
        }
    except cognito_client.exceptions.NotAuthorizedException:
        raise HTTPException(status_code=401, detail="Incorrect username or password")
    except cognito_client.exceptions.UserNotFoundException:
        raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
# @app.post("/upload-image", tags=["Compliance"])
# def upload_image(workspace_id: str, file: UploadFile = File(...)):
#     image_id = str(uuid4())
#     key = f"uploads/{workspace_id}/{image_id}-{file.filename}"

#     try:
#         s3_client.upload_fileobj(file.file, S3_BUCKET, key)
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"S3 upload failed: {str(e)}")

#     image_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{key}"

#     result = ComplianceResult(image_id=image_id, success=True, confidence=0.95, issues=[])
#     try:
#         ddb_table.put_item(Item={
#             "image_id": image_id,
#             "workspace_id": workspace_id,
#             "image_url": image_url,
#             "success": result.success,
#             "confidence": result.confidence,
#             "issues": result.issues
#         })
#     except ClientError as e:
#         raise HTTPException(status_code=500, detail=f"DynamoDB error: {e.response['Error']['Message']}")

#     return {"image_id": image_id, "image_url": image_url, "result": result}

# @app.get("/results/{image_id}", response_model=ComplianceResult, tags=["Compliance"])
# def get_result(image_id: str):
#     try:
#         response = ddb_table.get_item(Key={"image_id": image_id})
#         item = response.get("Item")
#         if not item:
#             raise HTTPException(404, "Result not found")
#         return ComplianceResult(
#             image_id=item["image_id"],
#             success=item["success"],
#             confidence=item["confidence"],
#             issues=item["issues"]
#         )
#     except ClientError as e:
#         raise HTTPException(status_code=500, detail=f"DynamoDB error: {e.response['Error']['Message']}")

@app.get("/rules/{workspace_id}", tags=["Rules"])
def get_rules(workspace_id: str):
    return []  # Placeholder

@app.get("/logs", tags=["Admin"])
def get_logs():
    return []  # Placeholder

# @app.get("/dashboard", tags=["Dashboard"])
# def get_dashboard():
#     try:
#         scan_response = ddb_table.scan()
#         items = scan_response.get("Items", [])
#         total = len(items)
#         success = sum(1 for item in items if item.get("success"))
#         avg_conf = sum(item.get("confidence", 0) for item in items) / total if total else 0
#         return {
#             "total_images": total,
#             "success_rate": round((success / total) * 100, 2) if total else 0,
#             "avg_confidence": round(avg_conf, 2)
#         }
#     except ClientError as e:
#         raise HTTPException(status_code=500, detail=f"DynamoDB error: {e.response['Error']['Message']}")

# Add this line after other app.include_router() calls or middleware setup
app.include_router(users_router)
app.include_router(workspaces_router)

if __name__=="__main__":
    import uvicorn
    uvicorn.run("__main__:app", host="0.0.0.0", port=8000, reload=True, workers=1)
else:
    handler = Mangum(app)
