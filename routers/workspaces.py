import base64
from fastapi import APIRouter, Request, HTTPException, Depends, Query
from typing import List, Optional
from pydantic import BaseModel
from boto3.dynamodb.conditions import Key
import boto3
import os
import json
from datetime import datetime
from uuid import uuid4
from botocore.exceptions import ClientError

from models import Workspace, WorkspaceStatus, WorkspaceUpdate, APIResponse
import traceback
from .utils import check_admin_access

router = APIRouter(prefix="/workspaces", tags=["Workspaces"])

# Status update model
class WorkspaceStatusUpdate(BaseModel):
    status: WorkspaceStatus

# AWS Setup
WORKSPACE_TABLE = os.getenv("WORKSPACES_TABLE", "Workspaces")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")

dynamodb = boto3.resource("dynamodb", region_name=AWS_REGION)
ddb_workspace_table = dynamodb.Table(WORKSPACE_TABLE)

@router.get("", tags=["Workspaces"])
def get_workspaces(
    request: Request,
    page_size: int = Query(10, gt=0, le=100),
    page_token: Optional[str] = Query(None),
    created_by: str = Query(...)
):
    try:
        check_admin_access(request)

        query_params = {
            "KeyConditionExpression": Key("created_by").eq(created_by),
            "Limit": page_size
        }

        if page_token:
            try:
                decoded = base64.urlsafe_b64decode(page_token.encode("utf-8"))
                query_params["ExclusiveStartKey"] = json.loads(decoded)
            except Exception:
                raise HTTPException(status_code=400, detail="Invalid pagination token")

        response = ddb_workspace_table.query(**query_params)
        items = response.get("Items", [])
        last_key = response.get("LastEvaluatedKey")

        next_page_token = None
        if last_key:
            next_page_token = base64.urlsafe_b64encode(
                json.dumps(last_key).encode("utf-8")
            ).decode("utf-8")

        return APIResponse(
            success=True,
            message="Workspaces retrieved successfully",
            data={
                "workspaces": items,
                "next_page_token": next_page_token,
                "has_more": bool(next_page_token)
            }
        )

    except HTTPException as e:
        raise e
    except ClientError as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"DynamoDB error: {e.response['Error']['Message']}"
        )
    except Exception:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("", tags=["Workspaces"])
def create_workspace(request: Request, workspace: Workspace):
    try:
        # Check admin access
        check_admin_access(request)

        workspace_id = str(uuid4())
        item = {
            "workspace_id": workspace_id,
            "name": workspace.name,
            "description": workspace.description,
            "created_at": datetime.now().isoformat(),
            "created_by": request.state.user["sub"],
            "status": WorkspaceStatus.ACTIVE
        }
        ddb_workspace_table.put_item(Item=item)
    except HTTPException as e:
        raise e
    except ClientError as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"DynamoDB error: {e.response['Error']['Message']}")
    return APIResponse(success=True, message="", data={"workspace_id": workspace_id})

@router.get("/{workspace_id}", tags=["Workspaces"])
def get_workspace(workspace_id: str, request: Request):
    try:
        # Check admin access
        check_admin_access(request)
        response = ddb_workspace_table.get_item(Key={"workspace_id": workspace_id})
        item = response.get("Item")
        if not item:
            raise HTTPException(404, "Workspace not found")
        return APIResponse(success=True, message="", data=item)
    except HTTPException as e:
        raise e
    except ClientError as e:
        raise HTTPException(status_code=500, detail=f"DynamoDB error: {e.response['Error']['Message']}")

@router.put("/{workspace_id}", tags=["Workspaces"])
def update_workspace(workspace_id: str, workspace_update: WorkspaceUpdate, request: Request):
    """Update workspace details (name, description, status)"""
    try:
        # Check admin access
        check_admin_access(request)

        # First check if workspace exists
        response = ddb_workspace_table.get_item(Key={"workspace_id": workspace_id})
        item = response.get("Item")
        if not item:
            raise HTTPException(status_code=404, detail="Workspace not found")

        # Build update expression dynamically based on provided fields
        update_expression = "SET updated_at = :updated_at"
        expression_attribute_values = {
            ":updated_at": datetime.now().isoformat()
        }

        if workspace_update.name is not None:
            update_expression += ", #name = :name"
            expression_attribute_values[":name"] = workspace_update.name

        if workspace_update.description is not None:
            update_expression += ", description = :description"
            expression_attribute_values[":description"] = workspace_update.description

        if workspace_update.status is not None:
            update_expression += ", #status = :status"
            expression_attribute_values[":status"] = workspace_update.status

        # Handle reserved keywords
        expression_attribute_names = {}
        if workspace_update.name is not None:
            expression_attribute_names["#name"] = "name"
        if workspace_update.status is not None:
            expression_attribute_names["#status"] = "status"

        # Perform the update
        update_params = {
            "Key": {"workspace_id": workspace_id},
            "UpdateExpression": update_expression,
            "ExpressionAttributeValues": expression_attribute_values,
            "ReturnValues": "ALL_NEW"
        }

        if expression_attribute_names:
            update_params["ExpressionAttributeNames"] = expression_attribute_names

        response = ddb_workspace_table.update_item(**update_params)
        updated_item = response.get("Attributes")

        return APIResponse(
            success=True,
            message="Workspace updated successfully",
            data=updated_item
        )

    except HTTPException as e:
        raise e
    except ClientError as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"DynamoDB error: {e.response['Error']['Message']}"
        )
    except Exception:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="Internal server error")

@router.patch("/{workspace_id}/status", tags=["Workspaces"])
def update_workspace_status(workspace_id: str, status_update: WorkspaceStatusUpdate, request: Request):
    """Update workspace status only"""
    try:
        # Check admin access
        check_admin_access(request)

        # First check if workspace exists
        response = ddb_workspace_table.get_item(Key={"workspace_id": workspace_id})
        item = response.get("Item")
        if not item:
            raise HTTPException(status_code=404, detail="Workspace not found")

        # Update only the status and updated_at timestamp
        response = ddb_workspace_table.update_item(
            Key={"workspace_id": workspace_id},
            UpdateExpression="SET #status = :status, updated_at = :updated_at",
            ExpressionAttributeNames={
                "#status": "status"
            },
            ExpressionAttributeValues={
                ":status": status_update.status,
                ":updated_at": datetime.now().isoformat()
            },
            ReturnValues="ALL_NEW"
        )

        updated_item = response.get("Attributes")

        return APIResponse(
            success=True,
            message="Workspace status updated successfully",
            data=updated_item
        )

    except HTTPException as e:
        raise e
    except ClientError as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"DynamoDB error: {e.response['Error']['Message']}"
        )
    except Exception:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="Internal server error")

