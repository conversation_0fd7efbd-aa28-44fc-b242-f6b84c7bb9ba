from fastapi import APIRouter, Request, HTTPException, Depends
from typing import List
from boto3.dynamodb.conditions import Key
import boto3
import os
from datetime import datetime
from uuid import uuid4
from botocore.exceptions import ClientError

from models import User, UserRole, UserUpdate, APIResponse
from pydantic import BaseModel
import traceback
from .utils import check_admin_access

router = APIRouter(prefix="/users", tags=["Users"])

# AWS Setup
USERS_TABLE = os.getenv("USERS_TABLE", "users")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
USER_POOL_ID = os.getenv("USER_POOL_ID")
DEFAULT_PASSWORD = os.getenv("DEFAULT_PASSWORD", "Aa@123456")

dynamodb = boto3.resource("dynamodb", region_name=AWS_REGION)
ddb_users_table = dynamodb.Table(USERS_TABLE)

cognito_client = boto3.client("cognito-idp", region_name=AWS_REGION)

# Status update model
class UserStatusUpdate(BaseModel):
    enabled: bool

def get_user_role(token):
    response = cognito_client.get_user(
        AccessToken=token
    )

    for attr in response['UserAttributes']:
        if attr['Name'] == 'custom:role':
            return attr['Value']


# @router.get("")
# async def get_users(request: Request):
#     try:
#         # Check admin access
#         check_admin_access(request)

#         response = ddb_users_table.scan()
#         users = response.get("Items", [])
#         return APIResponse(success=True, message="Users retrieved successfully", data=users)
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to retrieve users: {str(e)}")

@router.post("")
async def create_users(request: Request, user: User):
    try:
        check_admin_access(request)

        # Step 1: Create the user
        cognito_client.admin_create_user(
            UserPoolId=USER_POOL_ID,
            Username=user.user_name,
            UserAttributes=[
                {'Name': 'email', 'Value': user.email},
                {'Name': 'email_verified', 'Value': 'true'},
                {'Name': 'custom:role', 'Value': user.role}
            ],
            MessageAction='SUPPRESS'
        )

        # Step 2: Set a permanent password
        cognito_client.admin_set_user_password(
            UserPoolId=USER_POOL_ID,
            Username=user.user_name,
            Password=DEFAULT_PASSWORD,
            Permanent=True
        )

        # Step 3: Add user to DynamoDB
        user_id = str(uuid4())
        item = {
            "user_id": user_id,
            "user_name": user.user_name,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role,
            "created_at": datetime.now().isoformat(),
            "created_by": request.state.user["sub"]
        }
        ddb_users_table.put_item(Item=item)

    except Exception as e:
        print(f"Error occurred: {e}")
        traceback.print_exc()
        # Rollback: delete user if created
        try:
            cognito_client.admin_delete_user(
                UserPoolId=USER_POOL_ID,
                Username=user.user_name
            )
            print(f"User {user.user_name} deleted due to error rollback.")
        except ClientError as delete_error:
            print(f"Rollback failed: {delete_error}")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@router.get("")
async def get_users(
    request: Request,
    page_size: int = 10,
    page_token: str = None
):
    try:
        # Check admin access
        check_admin_access(request)

        users = []

        # Set up pagination parameters for Cognito
        params = {
            "UserPoolId": USER_POOL_ID,
            "Limit": page_size,
        }

        # Add pagination token if provided
        if page_token:
            params["PaginationToken"] = page_token

        # Get users from Cognito
        response = cognito_client.list_users(**params)
        users_raw = response.get("Users", [])
        for user in users_raw:
            # Check if user has custom:role attribute set to "admin" and skip if so
            user_attributes = user.get("Attributes", [])
            is_admin = False
            for attr in user_attributes:
                if attr["Name"] == "custom:role" and attr["Value"] == "admin":
                    is_admin = True
                    break

            # Skip admin users
            if is_admin:
                continue

            user_data = {
                "id": user.get("sub"),
                "username": user.get("Username"),
                "status": user.get("UserStatus"),
                "created_at": user.get("UserCreateDate").isoformat() if user.get("UserCreateDate") else None,
                "attributes": {
                    attr["Name"]: attr["Value"]
                    for attr in user_attributes
                }
            }
            users.append(user_data)

        # Get next page token
        next_page_token = response.get('PaginationToken')
        
        return APIResponse(
            success=True,
            message="Users retrieved successfully",
            data={
                "users": users,
                "next_page_token": next_page_token,
                "has_more": bool(next_page_token)
            }
        )
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to retrieve Cognito users: {str(e)}")


@router.get("/{user_id}")
async def get_user_detail(request: Request, username: str):
    """Get detailed information for a specific user"""
    try:
        # Check admin access
        check_admin_access(request)

        # Get user from Cognito
        response = cognito_client.admin_get_user(
            UserPoolId=USER_POOL_ID,
            Username=username
        )

        # Extract user attributes
        user_attributes = {}
        for attr in response.get("UserAttributes", []):
            user_attributes[attr["Name"]] = attr["Value"]

        # Build user detail response
        user_detail = {
            "username": response.get("Username"),
            "status": response.get("UserStatus"),
            "created_at": response.get("UserCreateDate").isoformat() if response.get("UserCreateDate") else None,
            "last_modified": response.get("UserLastModifiedDate").isoformat() if response.get("UserLastModifiedDate") else None,
            "email": user_attributes.get("email"),
            "email_verified": user_attributes.get("email_verified") == "true",
            "first_name": user_attributes.get("given_name"),
            "last_name": user_attributes.get("family_name"),
            "role": user_attributes.get("custom:role"),
        }

        return APIResponse(
            success=True,
            message="User details retrieved successfully",
            data=user_detail
        )

    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'UserNotFoundException':
            raise HTTPException(status_code=404, detail="User not found")
        else:
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user details: {str(e)}")


@router.put("/{username}")
async def update_user(request: Request, username: str, user_update: UserUpdate):
    """Update user information"""
    try:
        # Check admin access
        check_admin_access(request)

        # Build list of attributes to update
        attributes_to_update = []

        if user_update.email is not None:
            attributes_to_update.append({
                'Name': 'email',
                'Value': user_update.email
            })
            # When updating email, set email_verified to true
            attributes_to_update.append({
                'Name': 'email_verified',
                'Value': 'true'
            })

        if user_update.first_name is not None:
            attributes_to_update.append({
                'Name': 'given_name',
                'Value': user_update.first_name
            })

        if user_update.last_name is not None:
            attributes_to_update.append({
                'Name': 'family_name',
                'Value': user_update.last_name
            })

        if user_update.role is not None:
            attributes_to_update.append({
                'Name': 'custom:role',
                'Value': user_update.role.value
            })

        # Only proceed if there are attributes to update
        if not attributes_to_update:
            raise HTTPException(status_code=400, detail="No valid attributes provided for update")

        # Update user attributes in Cognito
        cognito_client.admin_update_user_attributes(
            UserPoolId=USER_POOL_ID,
            Username=username,
            UserAttributes=attributes_to_update
        )

        return APIResponse(
            success=True,
            message="User updated successfully",
            data=""
        )

    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'UserNotFoundException':
            raise HTTPException(status_code=404, detail="User not found")
        elif error_code == 'InvalidParameterException':
            raise HTTPException(status_code=400, detail="Invalid parameter provided")
        else:
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to update user: {str(e)}")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update user: {str(e)}")


@router.patch("/{username}/status")
async def update_user_status(request: Request, username: str, status_update: UserStatusUpdate):
    """Enable or disable a user account"""
    try:
        # Check admin access
        check_admin_access(request)

        # Enable or disable the user based on the provided status
        if status_update.enabled:
            # Enable the user
            cognito_client.admin_enable_user(
                UserPoolId=USER_POOL_ID,
                Username=username
            )
            action = "enabled"
        else:
            # Disable the user
            cognito_client.admin_disable_user(
                UserPoolId=USER_POOL_ID,
                Username=username
            )
            action = "disabled"

        return APIResponse(
            success=True,
            message=f"User {action} successfully",
            data=""
        )

    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'UserNotFoundException':
            raise HTTPException(status_code=404, detail="User not found")
        elif error_code == 'InvalidParameterException':
            raise HTTPException(status_code=400, detail="Invalid parameter provided")
        else:
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Failed to update user status: {str(e)}")
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to update user status: {str(e)}")

