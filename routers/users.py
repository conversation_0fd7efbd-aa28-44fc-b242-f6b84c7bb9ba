from fastapi import APIRouter, Request, HTTPException, Depends
from typing import List
from boto3.dynamodb.conditions import Key
import boto3
import os
from datetime import datetime
from uuid import uuid4
from botocore.exceptions import ClientError

from models import User, UserRole, APIResponse
import traceback
from .utils import check_admin_access

router = APIRouter(prefix="/users", tags=["Users"])

# AWS Setup
USERS_TABLE = os.getenv("USERS_TABLE", "users")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
USER_POOL_ID = os.getenv("USER_POOL_ID")
DEFAULT_PASSWORD = os.getenv("DEFAULT_PASSWORD", "Aa@123456")

dynamodb = boto3.resource("dynamodb", region_name=AWS_REGION)
ddb_users_table = dynamodb.Table(USERS_TABLE)

cognito_client = boto3.client("cognito-idp", region_name=AWS_REGION)

def get_user_role(token):
    response = cognito_client.get_user(
        AccessToken=token
    )

    for attr in response['UserAttributes']:
        if attr['Name'] == 'custom:role':
            return attr['Value']


# @router.get("")
# async def get_users(request: Request):
#     try:
#         # Check admin access
#         check_admin_access(request)

#         response = ddb_users_table.scan()
#         users = response.get("Items", [])
#         return APIResponse(success=True, message="Users retrieved successfully", data=users)
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to retrieve users: {str(e)}")

@router.post("")
async def create_users(request: Request, user: User):
    try:
        check_admin_access(request)

        # Step 1: Create the user
        cognito_client.admin_create_user(
            UserPoolId=USER_POOL_ID,
            Username=user.user_name,
            UserAttributes=[
                {'Name': 'email', 'Value': user.email},
                {'Name': 'email_verified', 'Value': 'true'},
                {'Name': 'custom:role', 'Value': user.role}
            ],
            MessageAction='SUPPRESS'
        )

        # Step 2: Set a permanent password
        cognito_client.admin_set_user_password(
            UserPoolId=USER_POOL_ID,
            Username=user.user_name,
            Password=DEFAULT_PASSWORD,
            Permanent=True
        )

        # Step 3: Add user to DynamoDB
        # user_id = str(uuid4())
        # item = {
        #     "user_id": user_id,
        #     "user_name": user.user_name,
        #     "email": user.email,
        #     "first_name": user.first_name,
        #     "last_name": user.last_name,
        #     "role": user.role,
        #     "created_at": datetime.now().isoformat(),
        #     "created_by": request.state.user["sub"]
        # }
        # ddb_users_table.put_item(Item=item)

    except Exception as e:
        print(f"Error occurred: {e}")
        traceback.print_exc()
        # Rollback: delete user if created
        try:
            cognito_client.admin_delete_user(
                UserPoolId=USER_POOL_ID,
                Username=user.user_name
            )
            print(f"User {user.user_name} deleted due to error rollback.")
        except ClientError as delete_error:
            print(f"Rollback failed: {delete_error}")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@router.get("")
async def get_users(
    request: Request,
    page_size: int = 10,
    page_token: str = None
):
    try:
        # Check admin access
        check_admin_access(request)

        users = []

        # Set up pagination parameters for Cognito
        params = {
            "UserPoolId": USER_POOL_ID,
            "Limit": page_size
        }

        # Add pagination token if provided
        if page_token:
            params["PaginationToken"] = page_token

        # Get users from Cognito
        response = cognito_client.list_users(**params)
        users_raw = response.get("Users", [])
        for user in users_raw:
            # Check if user has custom:role attribute set to "admin" and skip if so
            user_attributes = user.get("Attributes", [])
            is_admin = False
            for attr in user_attributes:
                if attr["Name"] == "custom:role" and attr["Value"] == "admin":
                    is_admin = True
                    break

            # Skip admin users
            if is_admin:
                continue

            user_data = {
                "username": user.get("Username"),
                "status": user.get("UserStatus"),
                "created_at": user.get("UserCreateDate").isoformat() if user.get("UserCreateDate") else None,
                "attributes": {
                    attr["Name"]: attr["Value"]
                    for attr in user_attributes
                }
            }
            users.append(user_data)

        # Get next page token
        next_page_token = response.get('PaginationToken')

        return APIResponse(
            success=True,
            message="Users retrieved successfully",
            data={
                "users": users,
                "next_page_token": next_page_token,
                "has_more": bool(next_page_token)
            }
        )
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to retrieve Cognito users: {str(e)}")

