{"name": "child-process-ext", "version": "2.1.1", "description": "Extensions to Node.js child_process module", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["child_process", "node", "spawn"], "repository": "medikoo/child-process-ext", "dependencies": {"cross-spawn": "^6.0.5", "es5-ext": "^0.10.53", "log": "^6.0.0", "split2": "^3.1.1", "stream-promise": "^3.2.0"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^6.8.0", "eslint-config-medikoo": "^2.7.0", "git-list-updated": "^1.2.1", "husky": "^4.2.3", "lint-staged": "^10.1.2", "mocha": "^6.2.3", "nyc": "^15.0.1", "prettier-elastic": "^1.19.1"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo", "root": true, "env": {"node": true}, "overrides": [{"files": "test/**/*.js", "env": {"mocha": true}}]}, "nyc": {"all": true, "exclude": ["coverage/**", "test/**"]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md"], "options": {"tabWidth": 2}}]}, "scripts": {"coverage": "nyc --reporter=lcov --reporter=html --reporter=text-summary npm test", "check-coverage": "npm run coverage && nyc check-coverage --statements 80 --function 80 --branches 50 --lines 80", "lint": "eslint --ignore-path=.gitignore .", "lint:updated": "pipe-git-updated --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "mocha --recursive"}, "license": "ISC"}