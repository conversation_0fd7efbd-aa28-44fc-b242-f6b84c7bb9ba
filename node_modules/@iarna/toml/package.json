{"name": "@iarna/toml", "version": "2.2.5", "main": "toml.js", "scripts": {"test": "tap -J --100 test/*.js test/toml-stream/*.js", "benchmark": "node benchmark.js && node benchmark-per-file.js && node results2table.js", "prerelease": "npm t", "prepack": "rm -f *~", "postpublish": "git push --follow-tags", "pretest": "iarna-standard", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'", "setup-burntsushi-toml-suite": "[ -d test/burntsushi-toml-test ] || (git clone https://github.com/BurntSushi/toml-test test/burntsushi-toml-test; rimraf test/burntsushi-toml-test/.git/hooks/*); cd test/burntsushi-toml-test; git pull", "setup-iarna-toml-suite": "[ -d test/spec-test ] || (git clone https://github.com/iarna/toml-spec-tests -b 0.5.0 test/spec-test; rimraf test/spec-test/.git/hooks/*); cd test/spec-test; git pull", "prepare": "npm run setup-burntsushi-toml-suite && npm run setup-iarna-toml-suite"}, "keywords": ["toml", "toml-parser", "toml-stringifier", "parser", "stringifer", "emitter", "ini", "tomlify", "encoder", "decoder"], "author": "<PERSON> <<EMAIL>> (http://re-becca.org/)", "license": "ISC", "description": "Better TOML parsing and stringifying all in that familiar JSON interface.", "dependencies": {}, "devDependencies": {"@iarna/standard": "^2.0.2", "@ltd/j-toml": "^0.5.107", "@perl/qx": "^1.1.0", "@sgarciac/bombadil": "^2.3.0", "ansi": "^0.3.1", "approximate-number": "^2.0.0", "benchmark": "^2.1.4", "fast-toml": "^0.5.4", "funstream": "^4.2.0", "glob": "^7.1.6", "js-yaml": "^3.13.1", "rimraf": "^3.0.2", "tap": "^12.0.1", "toml": "^3.0.0", "toml-j0.4": "^1.1.1", "weallbehave": "*", "weallcontribute": "*"}, "files": ["toml.js", "stringify.js", "parse.js", "parse-string.js", "parse-stream.js", "parse-async.js", "parse-pretty-error.js", "lib/parser.js", "lib/parser-debug.js", "lib/toml-parser.js", "lib/create-datetime.js", "lib/create-date.js", "lib/create-datetime-float.js", "lib/create-time.js", "lib/format-num.js", "index.d.ts"], "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/iarna-toml.git"}, "bugs": {"url": "https://github.com/iarna/iarna-toml/issues"}, "homepage": "https://github.com/iarna/iarna-toml#readme"}