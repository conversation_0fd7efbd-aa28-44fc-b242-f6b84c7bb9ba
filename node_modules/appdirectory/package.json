{"name": "appdirectory", "version": "0.1.0", "description": "A cross-platform utility to find the best directory to put data and config files.", "main": "lib/appdirectory.js", "repository": {"type": "git", "url": "http://github.com/MrJohz/appdirectory.git"}, "scripts": {"test": "make test"}, "keywords": ["cross-platform", "utility", "appdata", "config", "directory"], "author": "Jo<PERSON>z", "license": "MIT", "devDependencies": {"mocha": "~1.17.1", "should": "~3.1.3", "coveralls": "~2.8.0"}}