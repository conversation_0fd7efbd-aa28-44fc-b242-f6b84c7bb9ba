{"name": "duration", "version": "0.2.2", "description": "Time duration utilities", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["date", "duration", "time"], "repository": {"type": "git", "url": "git://github.com/medikoo/duration.git"}, "dependencies": {"d": "1", "es5-ext": "~0.10.46"}, "devDependencies": {"eslint": "^5.8", "eslint-config-medikoo-es5": "^1.7.2", "tad": "~0.2.8"}, "eslintConfig": {"extends": "medikoo-es5", "root": true, "rules": {"consistent-return": "off", "id-length": "off"}, "overrides": [{"files": "test/**", "rules": {"max-lines": "off", "max-statements": "off"}}]}, "scripts": {"lint": "eslint --ignore-path=.gitignore .", "test": "node ./node_modules/tad/bin/tad"}, "license": "ISC"}