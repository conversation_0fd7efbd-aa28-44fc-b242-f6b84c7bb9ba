#!/usr/bin/env python3
"""
Simple test script for the new workspace update APIs.
This script tests the PUT /workspaces/{workspace_id} and PATCH /workspaces/{workspace_id}/status endpoints.

To run this test:
1. Make sure your FastAPI server is running (python api.py or serverless offline)
2. Update the BASE_URL if needed
3. Run: python test_workspace_apis.py
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"  # Update this to match your server
TEST_WORKSPACE_NAME = f"Test Workspace {datetime.now().strftime('%Y%m%d_%H%M%S')}"

# Test data
TEST_USER_TOKEN = "your-test-token-here"  # You'll need to replace this with a valid token
HEADERS = {
    "Authorization": f"Bearer {TEST_USER_TOKEN}",
    "Content-Type": "application/json"
}

def print_test_result(test_name, success, message=""):
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if message:
        print(f"   {message}")

def test_create_workspace():
    """Create a test workspace to use for update tests"""
    print("\n🔧 Setting up test workspace...")
    
    payload = {
        "name": TEST_WORKSPACE_NAME,
        "description": "Test workspace for API testing"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/workspaces", json=payload, headers=HEADERS)
        if response.status_code == 200:
            data = response.json()
            workspace_id = data.get("data", {}).get("workspace_id")
            print(f"   Created workspace with ID: {workspace_id}")
            return workspace_id
        else:
            print(f"   Failed to create workspace: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"   Error creating workspace: {e}")
        return None

def test_update_workspace_full(workspace_id):
    """Test the PUT /workspaces/{workspace_id} endpoint"""
    print("\n📝 Testing full workspace update (PUT)...")
    
    payload = {
        "name": f"{TEST_WORKSPACE_NAME} - Updated",
        "description": "Updated description for testing",
        "status": "inactive"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/workspaces/{workspace_id}", json=payload, headers=HEADERS)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            updated_workspace = data.get("data", {})
            
            # Verify the updates
            name_correct = updated_workspace.get("name") == payload["name"]
            desc_correct = updated_workspace.get("description") == payload["description"]
            status_correct = updated_workspace.get("status") == payload["status"]
            has_updated_at = "updated_at" in updated_workspace
            
            all_correct = name_correct and desc_correct and status_correct and has_updated_at
            
            print_test_result(
                "Full workspace update", 
                all_correct,
                f"Name: {name_correct}, Desc: {desc_correct}, Status: {status_correct}, Updated_at: {has_updated_at}"
            )
            return all_correct
        else:
            print_test_result("Full workspace update", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_test_result("Full workspace update", False, f"Exception: {e}")
        return False

def test_update_workspace_partial(workspace_id):
    """Test partial update with PUT endpoint"""
    print("\n📝 Testing partial workspace update (PUT)...")
    
    payload = {
        "name": f"{TEST_WORKSPACE_NAME} - Partially Updated"
        # Only updating name, leaving description and status unchanged
    }
    
    try:
        response = requests.put(f"{BASE_URL}/workspaces/{workspace_id}", json=payload, headers=HEADERS)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            updated_workspace = data.get("data", {})
            
            # Verify the name was updated
            name_correct = updated_workspace.get("name") == payload["name"]
            has_updated_at = "updated_at" in updated_workspace
            
            print_test_result(
                "Partial workspace update", 
                name_correct and has_updated_at,
                f"Name updated: {name_correct}, Has updated_at: {has_updated_at}"
            )
            return name_correct and has_updated_at
        else:
            print_test_result("Partial workspace update", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_test_result("Partial workspace update", False, f"Exception: {e}")
        return False

def test_update_workspace_status_only(workspace_id):
    """Test the PATCH /workspaces/{workspace_id}/status endpoint"""
    print("\n📝 Testing status-only update (PATCH)...")
    
    payload = {
        "status": "active"
    }
    
    try:
        response = requests.patch(f"{BASE_URL}/workspaces/{workspace_id}/status", json=payload, headers=HEADERS)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            updated_workspace = data.get("data", {})
            
            # Verify the status was updated
            status_correct = updated_workspace.get("status") == payload["status"]
            has_updated_at = "updated_at" in updated_workspace
            
            print_test_result(
                "Status-only update", 
                status_correct and has_updated_at,
                f"Status updated: {status_correct}, Has updated_at: {has_updated_at}"
            )
            return status_correct and has_updated_at
        else:
            print_test_result("Status-only update", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_test_result("Status-only update", False, f"Exception: {e}")
        return False

def test_update_nonexistent_workspace():
    """Test updating a workspace that doesn't exist"""
    print("\n📝 Testing update of non-existent workspace...")
    
    fake_id = "non-existent-workspace-id"
    payload = {"name": "Should not work"}
    
    try:
        response = requests.put(f"{BASE_URL}/workspaces/{fake_id}", json=payload, headers=HEADERS)
        success = response.status_code == 404
        
        print_test_result(
            "Non-existent workspace update", 
            success,
            f"Expected 404, got {response.status_code}"
        )
        return success
            
    except Exception as e:
        print_test_result("Non-existent workspace update", False, f"Exception: {e}")
        return False

def cleanup_workspace(workspace_id):
    """Clean up the test workspace (if delete endpoint exists)"""
    print(f"\n🧹 Note: Test workspace {workspace_id} was created but not cleaned up.")
    print("   You may want to manually delete it if needed.")

def main():
    print("🚀 Starting Workspace API Tests")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print(f"❌ Server health check failed: {response.status_code}")
            sys.exit(1)
        print("✅ Server is running")
    except Exception as e:
        print(f"❌ Cannot connect to server at {BASE_URL}: {e}")
        print("   Make sure your FastAPI server is running")
        sys.exit(1)
    
    # Create test workspace
    workspace_id = test_create_workspace()
    if not workspace_id:
        print("❌ Cannot proceed without a test workspace")
        sys.exit(1)
    
    # Run tests
    test_results = []
    test_results.append(test_update_workspace_full(workspace_id))
    test_results.append(test_update_workspace_partial(workspace_id))
    test_results.append(test_update_workspace_status_only(workspace_id))
    test_results.append(test_update_nonexistent_workspace())
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    passed = sum(test_results)
    total = len(test_results)
    print(f"   Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        cleanup_workspace(workspace_id)
        sys.exit(0)
    else:
        print("❌ Some tests failed")
        cleanup_workspace(workspace_id)
        sys.exit(1)

if __name__ == "__main__":
    main()
