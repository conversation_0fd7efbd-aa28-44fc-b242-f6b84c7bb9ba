from pydantic import BaseModel, Field
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

# Enums
class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"

class WorkspaceStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"

class ComplianceStatus(str, Enum):
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING = "pending"

class LoginRequest(BaseModel):
    username: str
    password: str

class User(BaseModel):
    user_name: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: UserRole = UserRole.USER

class UserUpdate(BaseModel):
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[UserRole] = None

class Workspace(BaseModel):
    name: str
    description: Optional[str] = None
    status: WorkspaceStatus = WorkspaceStatus.ACTIVE

class ComplianceRule(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workspace_id: str
    rule_name: str
    rule_description: str
    criteria: Dict[str, Any]
    threshold: float = Field(ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.now)

class ReferenceImage(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workspace_id: str
    image_name: str
    image_url: str
    description: Optional[str] = None
    uploaded_at: datetime = Field(default_factory=datetime.now)

class ComplianceResult(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workspace_id: str
    image_id: str
    status: ComplianceStatus
    confidence_score: float = Field(ge=0.0, le=1.0)
    details: Dict[str, Any]
    location: Optional[str] = None
    agent_id: Optional[str] = None
    processed_at: datetime = Field(default_factory=datetime.now)

class ComplianceSubmission(BaseModel):
    workspace_id: str
    location: Optional[str] = None
    agent_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class DashboardMetrics(BaseModel):
    total_images: int
    success_rate: float
    average_confidence: float
    compliant_count: int
    non_compliant_count: int
    pending_count: int
    last_updated: datetime

# Request/Response Models
class WorkspaceCreate(BaseModel):
    name: str
    description: Optional[str] = None

class WorkspaceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[WorkspaceStatus] = None

class ComplianceRuleCreate(BaseModel):
    rule_name: str
    rule_description: str
    criteria: Dict[str, Any]
    threshold: float = Field(ge=0.0, le=1.0)

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    timestamp: datetime = Field(default_factory=datetime.now)
